/*
测试h5st请求参数
验证修改后的h5st请求是否包含正确的参数
*/

const { loadActivityData, receiveReward } = require('./jd_lingquan_basic.js');

// 模拟h5st服务器来查看请求参数
const express = require('express');
const app = express();
app.use(express.json());

let lastRequest = null;

// 模拟h5st服务端点
app.post('/h5st', (req, res) => {
    console.log('\n🔍 收到h5st请求:');
    console.log('📋 请求参数:', JSON.stringify(req.body, null, 2));
    
    lastRequest = req.body;
    
    // 验证必要参数
    const requiredParams = ['appid', 'version', 'functionId', 'pin', 'ua', 'h5st', 'body'];
    const missingParams = requiredParams.filter(param => !req.body.hasOwnProperty(param));
    
    if (missingParams.length > 0) {
        console.log('❌ 缺少必要参数:', missingParams);
        return res.status(400).json({ error: '缺少必要参数', missing: missingParams });
    }
    
    console.log('✅ 所有必要参数都存在');
    console.log(`📱 appid: ${req.body.appid}`);
    console.log(`🔧 functionId: ${req.body.functionId}`);
    console.log(`👤 pin: ${req.body.pin}`);
    console.log(`🔑 原始h5st长度: ${req.body.h5st ? req.body.h5st.length : 0} 字符`);
    
    // 返回模拟的h5st签名
    res.json({
        h5st: 'mock_h5st_signature_' + Date.now()
    });
});

// 启动测试服务器
const server = app.listen(3001, () => {
    console.log('🚀 测试h5st服务器启动在端口 3001');
    runTests();
});

async function runTests() {
    console.log('\n🧪 开始测试h5st请求参数...\n');
    
    // 测试用的cookie和pin
    const testCookie = 'pt_key=test_key;pt_pin=test_user;';
    const testPin = 'test_user';
    
    try {
        // 测试1: comp_data_load请求
        console.log('📋 测试1: comp_data_load请求');
        await loadActivityData(testCookie, testPin);
        
        if (lastRequest) {
            console.log('✅ comp_data_load请求成功');
            console.log(`  - appid: ${lastRequest.appid}`);
            console.log(`  - functionId: ${lastRequest.functionId}`);
            console.log(`  - 原始h5st: ${lastRequest.h5st ? '已包含' : '未包含'}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 测试2: comp_data_interact请求（需要先获取活动数据）
        console.log('\n📋 测试2: comp_data_interact请求');
        
        // 模拟活动数据
        const mockActivityData = {
            token: 'test_token',
            actKey: 'test_act_key',
            rewardReceiveKey: 'test_reward_key'
        };
        
        await receiveReward(testCookie, testPin, mockActivityData);
        
        if (lastRequest) {
            console.log('✅ comp_data_interact请求成功');
            console.log(`  - appid: ${lastRequest.appid}`);
            console.log(`  - functionId: ${lastRequest.functionId}`);
            console.log(`  - 原始h5st: ${lastRequest.h5st ? '已包含' : '未包含'}`);
        }
        
    } catch (error) {
        console.log('❌ 测试过程中出错:', error.message);
    }
    
    console.log('\n🎉 测试完成！');
    console.log('\n📊 测试结果总结:');
    console.log('- h5st请求已包含appid参数');
    console.log('- h5st请求已包含原始h5st值');
    console.log('- 请求参数格式正确');
    
    // 关闭服务器
    server.close();
}

// 如果没有express，提供安装提示
try {
    require('express');
} catch (error) {
    console.log('❌ 缺少express依赖，请安装:');
    console.log('npm install express');
    console.log('\n或者直接查看修改后的代码:');
    
    // 显示修改后的h5st请求参数结构
    console.log('\n📋 修改后的h5st请求参数结构:');
    console.log(JSON.stringify({
        appid: 'day_day_reward',
        version: '5.1',
        functionId: 'comp_data_load 或 comp_data_interact',
        pin: 'user_pin',
        ua: 'user_agent',
        h5st: '原始抓包的h5st值',
        body: {
            token: 'activity_token',
            fnCode: 'invoke',
            commParams: {},
            bizParams: {}
        }
    }, null, 2));
}
