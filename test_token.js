/*
测试token获取功能
验证从指定URL获取x-api-eid-token的功能
*/

const { getApiEidToken } = require('./jd_lingquan_basic.js');

async function testTokenService() {
    console.log('🧪 开始测试token获取服务...\n');
    
    try {
        // 测试1: 基本token获取
        console.log('📋 测试1: 基本token获取');
        const token = await getApiEidToken();
        
        if (token) {
            console.log('✅ token获取成功');
            console.log(`📋 token长度: ${token.length} 字符`);
            console.log(`📋 token前50字符: ${token.substring(0, 50)}...`);
            
            // 验证token格式
            if (token.startsWith('jdd')) {
                console.log('✅ token格式正确 (以jdd开头)');
            } else {
                console.log('⚠️  token格式可能异常 (不以jdd开头)');
            }
        } else {
            console.log('❌ token获取失败');
        }
        
        console.log('\n' + '='.repeat(50));
        
        // 测试2: 多次获取验证稳定性
        console.log('\n📋 测试2: 多次获取验证稳定性');
        const tokens = [];
        
        for (let i = 1; i <= 3; i++) {
            console.log(`  第${i}次获取...`);
            const testToken = await getApiEidToken();
            tokens.push(testToken);
            
            if (testToken) {
                console.log(`  ✅ 第${i}次成功: ${testToken.substring(0, 30)}...`);
            } else {
                console.log(`  ❌ 第${i}次失败`);
            }
            
            // 延迟1秒
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // 分析结果
        const validTokens = tokens.filter(t => t && t.length > 0);
        console.log(`\n📊 结果统计:`);
        console.log(`  - 总请求次数: ${tokens.length}`);
        console.log(`  - 成功次数: ${validTokens.length}`);
        console.log(`  - 成功率: ${(validTokens.length / tokens.length * 100).toFixed(1)}%`);
        
        // 检查token是否相同
        if (validTokens.length > 1) {
            const allSame = validTokens.every(token => token === validTokens[0]);
            if (allSame) {
                console.log('  - Token一致性: ✅ 所有token相同');
            } else {
                console.log('  - Token一致性: ⚠️  token不同 (可能是动态生成)');
            }
        }
        
        console.log('\n' + '='.repeat(50));
        
        // 测试3: 模拟实际使用场景
        console.log('\n📋 测试3: 模拟实际使用场景');
        console.log('模拟在实际请求中使用token...');
        
        const finalToken = await getApiEidToken();
        if (finalToken) {
            console.log('✅ 实际使用场景token获取成功');
            
            // 模拟构建请求参数
            const mockParams = {
                appid: 'day_day_reward',
                functionId: 'comp_data_load',
                'x-api-eid-token': finalToken
            };
            
            console.log('📋 模拟请求参数构建成功:');
            console.log(`  - appid: ${mockParams.appid}`);
            console.log(`  - functionId: ${mockParams.functionId}`);
            console.log(`  - x-api-eid-token: ${finalToken.substring(0, 30)}...`);
        } else {
            console.log('❌ 实际使用场景token获取失败');
        }
        
    } catch (error) {
        console.log('❌ 测试过程中出错:', error.message);
    }
    
    console.log('\n🎉 token获取功能测试完成！');
    console.log('\n💡 使用说明:');
    console.log('1. 确保token服务正常运行在 http://10.0.0.111:4444/getJsToken');
    console.log('2. 服务应返回格式: {"success":true,"token":{"token":"","gia_d":1,"deMap":null,"ds":120}}');
    console.log('3. 脚本会自动获取token.token字段作为x-api-eid-token的值');
    console.log('4. 如果获取失败，会使用默认的备用token');
}

// 测试服务连接
async function testServiceConnection() {
    console.log('🔍 测试token服务连接...');
    
    const axios = require('axios');
    const tokenUrl = 'http://10.0.0.111:4444/getJsToken';
    
    try {
        const response = await axios.get(tokenUrl, { timeout: 5000 });
        console.log('✅ 服务连接正常');
        console.log('📋 响应状态:', response.status);
        console.log('📋 响应数据:', JSON.stringify(response.data, null, 2));
        
        // 验证响应格式
        if (response.data && response.data.success && response.data.token) {
            console.log('✅ 响应格式正确');
            if (response.data.token.token) {
                console.log('✅ token字段存在');
            } else {
                console.log('⚠️  token.token字段为空');
            }
        } else {
            console.log('❌ 响应格式不正确');
        }
        
    } catch (error) {
        console.log('❌ 服务连接失败:', error.message);
        console.log('💡 请检查:');
        console.log('  1. 服务是否正在运行');
        console.log('  2. 地址是否正确: ' + tokenUrl);
        console.log('  3. 网络连接是否正常');
    }
}

// 主函数
async function main() {
    console.log('🚀 token获取功能测试工具\n');
    
    // 先测试服务连接
    await testServiceConnection();
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // 再测试token获取功能
    await testTokenService();
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testTokenService, testServiceConnection };
