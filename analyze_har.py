#!/usr/bin/env python3
"""
HAR文件分析工具
用于分析ProxyPin6-8_19_43_43.har文件，查找comp_data_load之前的请求
"""

import json
import sys
from datetime import datetime

def parse_har_file(filename):
    """解析HAR文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            har_data = json.load(f)
        return har_data
    except Exception as e:
        print(f"❌ 解析HAR文件失败: {e}")
        return None

def analyze_requests(har_data):
    """分析请求序列"""
    if not har_data or 'log' not in har_data:
        print("❌ HAR文件格式错误")
        return
    
    entries = har_data['log']['entries']
    print(f"📊 总共找到 {len(entries)} 个请求\n")
    
    # 按时间排序
    entries.sort(key=lambda x: x['startedDateTime'])
    
    comp_data_load_found = False
    comp_data_load_index = -1
    
    print("🔍 请求序列分析:")
    print("=" * 80)
    
    for i, entry in enumerate(entries):
        request = entry['request']
        url = request['url']
        method = request['method']
        start_time = entry['startedDateTime']
        
        # 检查是否是comp_data_load请求
        if 'comp_data_load' in url or (request.get('postData') and 'comp_data_load' in str(request['postData'])):
            comp_data_load_found = True
            comp_data_load_index = i
            print(f"🎯 [{i+1:2d}] {start_time} | {method:4s} | comp_data_load 请求")
            print(f"     URL: {url}")
            
            # 分析请求体
            if request.get('postData'):
                post_data = request['postData']
                if post_data.get('text'):
                    try:
                        # 尝试解析URL编码的数据
                        from urllib.parse import parse_qs, unquote
                        if 'body=' in post_data['text']:
                            body_param = post_data['text'].split('body=')[1].split('&')[0]
                            body_json = json.loads(unquote(body_param))
                            print(f"     📋 请求体: {json.dumps(body_json, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"     📋 请求体: {post_data['text'][:200]}...")
            print()
        
        # 检查是否是API请求
        elif 'api.m.jd.com' in url or 'client.action' in url:
            status = "🔍" if not comp_data_load_found else "⏭️"
            print(f"{status} [{i+1:2d}] {start_time} | {method:4s} | API请求")
            print(f"     URL: {url}")
            
            # 分析functionId
            if request.get('postData'):
                post_data = request['postData']
                if post_data.get('text'):
                    try:
                        from urllib.parse import parse_qs, unquote
                        if 'functionId=' in post_data['text']:
                            function_id = post_data['text'].split('functionId=')[1].split('&')[0]
                            print(f"     🔧 functionId: {unquote(function_id)}")
                        if 'body=' in post_data['text']:
                            body_param = post_data['text'].split('body=')[1].split('&')[0]
                            try:
                                body_json = json.loads(unquote(body_param))
                                if 'bizParams' in body_json and 'actKey' in body_json['bizParams']:
                                    print(f"     🔑 actKey: {body_json['bizParams']['actKey']}")
                                if 'token' in body_json:
                                    print(f"     🎫 token: {body_json['token']}")
                            except:
                                pass
                    except:
                        pass
            print()
        
        # 其他重要请求
        elif any(keyword in url.lower() for keyword in ['activity', 'token', 'auth', 'login', 'init']):
            status = "📌" if not comp_data_load_found else "⏭️"
            print(f"{status} [{i+1:2d}] {start_time} | {method:4s} | 相关请求")
            print(f"     URL: {url}")
            print()
    
    print("=" * 80)
    
    if comp_data_load_found:
        print(f"\n✅ 找到 comp_data_load 请求，位置: 第 {comp_data_load_index + 1} 个")
        print(f"📋 在 comp_data_load 之前有 {comp_data_load_index} 个请求")
        
        if comp_data_load_index > 0:
            print(f"\n🔍 comp_data_load 之前的关键请求:")
            for i in range(comp_data_load_index):
                entry = entries[i]
                request = entry['request']
                url = request['url']
                
                # 只显示可能获取actKey或token的请求
                if any(keyword in url.lower() for keyword in ['api.m.jd.com', 'client.action', 'activity', 'token', 'auth', 'init']):
                    print(f"  [{i+1:2d}] {request['method']} {url}")
                    
                    # 分析响应
                    if 'response' in entry:
                        response = entry['response']
                        if response.get('content') and response['content'].get('text'):
                            try:
                                response_text = response['content']['text']
                                response_json = json.loads(response_text)
                                
                                # 查找actKey
                                if isinstance(response_json, dict):
                                    def find_keys(obj, target_keys=['actKey', 'token', 'actEncKey']):
                                        results = {}
                                        if isinstance(obj, dict):
                                            for key, value in obj.items():
                                                if key in target_keys:
                                                    results[key] = value
                                                elif isinstance(value, (dict, list)):
                                                    results.update(find_keys(value, target_keys))
                                        elif isinstance(obj, list):
                                            for item in obj:
                                                results.update(find_keys(item, target_keys))
                                        return results
                                    
                                    found_keys = find_keys(response_json)
                                    if found_keys:
                                        print(f"      🎯 响应中找到: {found_keys}")
                            except:
                                pass
        else:
            print("\n📝 comp_data_load 是第一个请求，没有前置请求")
    else:
        print("\n❌ 未找到 comp_data_load 请求")

def main():
    filename = "ProxyPin6-8_19_43_43.har"
    print(f"🔍 分析HAR文件: {filename}")
    print("=" * 60)
    
    har_data = parse_har_file(filename)
    if har_data:
        analyze_requests(har_data)
    else:
        print("❌ 无法分析HAR文件")

if __name__ == "__main__":
    main()
