/*
dy.js网络请求监控脚本
用于捕获和分析dy.js运行时的所有网络请求
*/

const http = require('http');
const https = require('https');
const { spawn } = require('child_process');
const fs = require('fs');

// 存储所有请求的数组
let capturedRequests = [];
let requestCounter = 0;

// 保存原始的request方法
const originalHttpRequest = http.request;
const originalHttpsRequest = https.request;

// 拦截HTTP请求
function interceptRequest(originalRequest, protocol) {
    return function(options, callback) {
        requestCounter++;
        const requestId = requestCounter;
        const startTime = Date.now();
        
        // 记录请求信息
        const requestInfo = {
            id: requestId,
            protocol: protocol,
            timestamp: new Date().toISOString(),
            method: options.method || 'GET',
            url: `${protocol}://${options.hostname || options.host}${options.path || '/'}`,
            headers: options.headers || {},
            startTime: startTime
        };
        
        console.log(`\n🔍 [${requestId}] ${requestInfo.method} ${requestInfo.url}`);
        console.log(`📅 时间: ${requestInfo.timestamp}`);
        console.log(`📋 请求头:`, JSON.stringify(requestInfo.headers, null, 2));
        
        // 拦截请求体
        const originalWrite = options.write;
        let requestBody = '';
        
        const req = originalRequest.call(this, options, (res) => {
            const endTime = Date.now();
            requestInfo.duration = endTime - startTime;
            requestInfo.statusCode = res.statusCode;
            requestInfo.responseHeaders = res.headers;
            
            console.log(`📤 [${requestId}] 响应状态: ${res.statusCode}`);
            console.log(`⏱️  耗时: ${requestInfo.duration}ms`);
            console.log(`📋 响应头:`, JSON.stringify(res.headers, null, 2));
            
            // 拦截响应体
            let responseBody = '';
            const originalOn = res.on;
            
            res.on = function(event, listener) {
                if (event === 'data') {
                    const originalListener = listener;
                    listener = function(chunk) {
                        responseBody += chunk.toString();
                        return originalListener.call(this, chunk);
                    };
                }
                return originalOn.call(this, event, listener);
            };
            
            res.on('end', () => {
                requestInfo.requestBody = requestBody;
                requestInfo.responseBody = responseBody;
                
                console.log(`📥 [${requestId}] 请求体:`, requestBody || '(空)');
                console.log(`📥 [${requestId}] 响应体:`, responseBody.substring(0, 500) + (responseBody.length > 500 ? '...' : ''));
                console.log(`${'='.repeat(80)}`);
                
                capturedRequests.push(requestInfo);
            });
            
            if (callback) callback(res);
        });
        
        // 拦截写入请求体
        const originalReqWrite = req.write;
        req.write = function(chunk) {
            if (chunk) {
                requestBody += chunk.toString();
            }
            return originalReqWrite.call(this, chunk);
        };
        
        return req;
    };
}

// 设置拦截器
http.request = interceptRequest(originalHttpRequest, 'http');
https.request = interceptRequest(originalHttpsRequest, 'https');

console.log('🚀 开始监控dy.js的网络请求...\n');

// 运行dy.js脚本
const dyProcess = spawn('node', ['dy.js'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    env: process.env
});

// 输出dy.js的标准输出
dyProcess.stdout.on('data', (data) => {
    console.log(`[dy.js输出] ${data.toString()}`);
});

// 输出dy.js的错误输出
dyProcess.stderr.on('data', (data) => {
    console.error(`[dy.js错误] ${data.toString()}`);
});

// dy.js进程结束时的处理
dyProcess.on('close', (code) => {
    console.log(`\n🏁 dy.js进程结束，退出码: ${code}`);
    console.log(`📊 总共捕获了 ${capturedRequests.length} 个网络请求\n`);
    
    // 生成分析报告
    generateAnalysisReport();
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n⏹️  监控被中断');
    dyProcess.kill();
    generateAnalysisReport();
    process.exit(0);
});

// 生成分析报告
function generateAnalysisReport() {
    console.log('📋 ========== 网络请求分析报告 ==========\n');
    
    if (capturedRequests.length === 0) {
        console.log('❌ 未捕获到任何网络请求');
        return;
    }
    
    // 按时间排序
    capturedRequests.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    
    console.log(`📊 请求统计:`);
    console.log(`  - 总请求数: ${capturedRequests.length}`);
    console.log(`  - HTTP请求: ${capturedRequests.filter(r => r.protocol === 'http').length}`);
    console.log(`  - HTTPS请求: ${capturedRequests.filter(r => r.protocol === 'https').length}`);
    
    // 按域名分组
    const domainGroups = {};
    capturedRequests.forEach(req => {
        const url = new URL(req.url);
        const domain = url.hostname;
        if (!domainGroups[domain]) {
            domainGroups[domain] = [];
        }
        domainGroups[domain].push(req);
    });
    
    console.log(`\n🌐 域名统计:`);
    Object.keys(domainGroups).forEach(domain => {
        console.log(`  - ${domain}: ${domainGroups[domain].length} 个请求`);
    });
    
    console.log(`\n📝 详细请求列表:`);
    capturedRequests.forEach((req, index) => {
        console.log(`\n[${index + 1}] ${req.method} ${req.url}`);
        console.log(`    时间: ${req.timestamp}`);
        console.log(`    状态: ${req.statusCode || '未完成'}`);
        console.log(`    耗时: ${req.duration || '未知'}ms`);
        
        // 显示关键请求头
        const importantHeaders = ['user-agent', 'cookie', 'referer', 'content-type', 'authorization'];
        importantHeaders.forEach(header => {
            if (req.headers[header]) {
                console.log(`    ${header}: ${req.headers[header]}`);
            }
        });
        
        // 显示请求体（如果有）
        if (req.requestBody && req.requestBody.trim()) {
            console.log(`    请求体: ${req.requestBody.substring(0, 200)}${req.requestBody.length > 200 ? '...' : ''}`);
        }
        
        // 显示响应体摘要（如果有）
        if (req.responseBody && req.responseBody.trim()) {
            console.log(`    响应体: ${req.responseBody.substring(0, 200)}${req.responseBody.length > 200 ? '...' : ''}`);
        }
    });
    
    // 保存详细数据到文件
    const reportData = {
        summary: {
            totalRequests: capturedRequests.length,
            httpRequests: capturedRequests.filter(r => r.protocol === 'http').length,
            httpsRequests: capturedRequests.filter(r => r.protocol === 'https').length,
            domains: Object.keys(domainGroups).map(domain => ({
                domain: domain,
                requestCount: domainGroups[domain].length
            }))
        },
        requests: capturedRequests
    };
    
    const reportFile = `dy_requests_${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
    console.log(`\n💾 详细报告已保存到: ${reportFile}`);
    
    // 分析京东相关请求
    analyzeJDRequests();
}

// 分析京东相关请求
function analyzeJDRequests() {
    const jdRequests = capturedRequests.filter(req => 
        req.url.includes('jd.com') || 
        req.url.includes('360buy') ||
        req.headers['user-agent']?.includes('jd')
    );
    
    if (jdRequests.length === 0) {
        console.log('\n❌ 未发现京东相关请求');
        return;
    }
    
    console.log(`\n🛒 ========== 京东请求分析 ==========`);
    console.log(`📊 京东相关请求: ${jdRequests.length} 个\n`);
    
    jdRequests.forEach((req, index) => {
        console.log(`[JD-${index + 1}] ${req.method} ${req.url}`);
        
        // 分析URL参数
        try {
            const url = new URL(req.url);
            if (url.search) {
                console.log(`    URL参数: ${url.search}`);
            }
        } catch (e) {
            // URL解析失败，忽略
        }
        
        // 分析Cookie
        if (req.headers.cookie) {
            const cookies = req.headers.cookie.split(';').map(c => c.trim());
            const importantCookies = cookies.filter(c => 
                c.startsWith('pt_key=') || 
                c.startsWith('pt_pin=') ||
                c.startsWith('sid=') ||
                c.startsWith('token=')
            );
            if (importantCookies.length > 0) {
                console.log(`    重要Cookie: ${importantCookies.join('; ')}`);
            }
        }
        
        // 分析请求体中的关键信息
        if (req.requestBody) {
            const body = req.requestBody;
            if (body.includes('functionId')) {
                const functionIdMatch = body.match(/functionId=([^&]+)/);
                if (functionIdMatch) {
                    console.log(`    functionId: ${decodeURIComponent(functionIdMatch[1])}`);
                }
            }
            if (body.includes('body=')) {
                const bodyMatch = body.match(/body=([^&]+)/);
                if (bodyMatch) {
                    try {
                        const bodyData = JSON.parse(decodeURIComponent(bodyMatch[1]));
                        console.log(`    业务参数: ${JSON.stringify(bodyData, null, 4)}`);
                    } catch (e) {
                        console.log(`    业务参数: ${decodeURIComponent(bodyMatch[1]).substring(0, 100)}...`);
                    }
                }
            }
        }
        
        console.log('');
    });
}
