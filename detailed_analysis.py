#!/usr/bin/env python3
"""
详细分析HAR文件中comp_data_load之前的请求
重点查找获取actKey和token的请求
"""

import json
from urllib.parse import parse_qs, unquote

def analyze_detailed():
    """详细分析HAR文件"""
    try:
        with open("ProxyPin6-8_19_43_43.har", 'r', encoding='utf-8') as f:
            har_data = json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    entries = har_data['log']['entries']
    entries.sort(key=lambda x: x['startedDateTime'])
    
    print("🔍 详细分析 comp_data_load 之前的关键请求")
    print("=" * 80)
    
    # 找到comp_data_load的位置
    comp_data_load_index = -1
    for i, entry in enumerate(entries):
        if 'comp_data_load' in entry['request']['url'] or (
            entry['request'].get('postData') and 
            'comp_data_load' in str(entry['request']['postData'])
        ):
            comp_data_load_index = i
            break
    
    if comp_data_load_index == -1:
        print("❌ 未找到 comp_data_load 请求")
        return
    
    print(f"✅ comp_data_load 位置: 第 {comp_data_load_index + 1} 个请求")
    
    # 分析comp_data_load请求本身
    comp_entry = entries[comp_data_load_index]
    print(f"\n📋 comp_data_load 请求详情:")
    print(f"时间: {comp_entry['startedDateTime']}")
    print(f"URL: {comp_entry['request']['url']}")
    
    if comp_entry['request'].get('postData'):
        post_data = comp_entry['request']['postData']['text']
        try:
            if 'body=' in post_data:
                body_param = post_data.split('body=')[1].split('&')[0]
                body_json = json.loads(unquote(body_param))
                print(f"请求体:")
                print(json.dumps(body_json, ensure_ascii=False, indent=2))
                
                # 提取关键信息
                token = body_json.get('token', '')
                act_key = body_json.get('bizParams', {}).get('actKey', '')
                print(f"\n🎯 关键参数:")
                print(f"  token: {token}")
                print(f"  actKey: {act_key}")
        except Exception as e:
            print(f"解析请求体失败: {e}")
    
    print(f"\n🔍 分析前 {comp_data_load_index} 个请求:")
    print("=" * 80)
    
    # 分析前面的请求
    for i in range(comp_data_load_index):
        entry = entries[i]
        request = entry['request']
        url = request['url']
        method = request['method']
        
        # 只关注API请求
        if not any(keyword in url.lower() for keyword in ['api.m.jd.com', 'client.action']):
            continue
        
        print(f"\n[{i+1:2d}] {method} {url}")
        print(f"时间: {entry['startedDateTime']}")
        
        # 分析请求参数
        if request.get('postData'):
            post_data = request['postData']['text']
            try:
                if 'functionId=' in post_data:
                    function_id = post_data.split('functionId=')[1].split('&')[0]
                    print(f"functionId: {unquote(function_id)}")
                
                if 'body=' in post_data:
                    body_param = post_data.split('body=')[1].split('&')[0]
                    try:
                        body_json = json.loads(unquote(body_param))
                        print(f"请求体: {json.dumps(body_json, ensure_ascii=False, indent=2)}")
                    except:
                        print(f"请求体: {unquote(body_param)[:200]}...")
            except:
                print(f"原始POST数据: {post_data[:200]}...")
        
        # 分析响应
        if 'response' in entry and entry['response'].get('content'):
            response_content = entry['response']['content']
            if response_content.get('text'):
                try:
                    response_json = json.loads(response_content['text'])
                    print(f"响应: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
                    
                    # 查找关键字段
                    def find_important_fields(obj, path=""):
                        results = []
                        if isinstance(obj, dict):
                            for key, value in obj.items():
                                current_path = f"{path}.{key}" if path else key
                                if key.lower() in ['actkey', 'token', 'actenckey', 'encodeactivityid']:
                                    results.append(f"{current_path}: {value}")
                                elif isinstance(value, (dict, list)):
                                    results.extend(find_important_fields(value, current_path))
                        elif isinstance(obj, list):
                            for idx, item in enumerate(obj):
                                results.extend(find_important_fields(item, f"{path}[{idx}]"))
                        return results
                    
                    important_fields = find_important_fields(response_json)
                    if important_fields:
                        print("🎯 重要字段:")
                        for field in important_fields:
                            print(f"  {field}")
                
                except Exception as e:
                    response_text = response_content['text']
                    print(f"响应 (非JSON): {response_text[:200]}...")
        
        print("-" * 60)
    
    print(f"\n📊 总结:")
    print(f"- comp_data_load 使用的 token: pVQSJCqPp3oXPfH9Y28Tv")
    print(f"- comp_data_load 使用的 actKey: iadem13cpdiof3vtyykmr")
    print(f"- 需要查找这些参数是从哪个前置请求获取的")

if __name__ == "__main__":
    analyze_detailed()
