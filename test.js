/*
测试脚本 - 验证代码功能
*/

const { CookieJDs, extractPin, validateCookie } = require('./jdCookie.js');

console.log('🧪 开始测试京东领券中心抽奖脚本...\n');

// 测试1: Cookie验证功能
console.log('📋 测试1: Cookie验证功能');
const testCookies = [
    'pt_key=test123;pt_pin=testuser;',  // 有效
    'pt_key=test123;',                   // 无效（缺少pt_pin）
    'pt_pin=testuser;',                  // 无效（缺少pt_key）
    '',                                  // 无效（空字符串）
    'invalid_cookie'                     // 无效（格式错误）
];

testCookies.forEach((cookie, index) => {
    const isValid = validateCookie(cookie);
    const pin = extractPin(cookie);
    console.log(`  Cookie ${index + 1}: ${isValid ? '✅ 有效' : '❌ 无效'} | Pin: ${pin || '无'}`);
});

// 测试2: 配置加载
console.log('\n📋 测试2: 配置加载');
console.log(`  从配置文件加载的账号数: ${CookieJDs.length}`);
console.log(`  环境变量JD_COOKIE: ${process.env.JD_COOKIE ? '已设置' : '未设置'}`);

// 测试3: 模块导入
console.log('\n📋 测试3: 模块导入测试');
try {
    const basicModule = require('./jd_lingquan_basic.js');
    console.log('  ✅ jd_lingquan_basic.js 导入成功');
    console.log(`  - 导出函数: ${Object.keys(basicModule).join(', ')}`);
} catch (error) {
    console.log('  ❌ jd_lingquan_basic.js 导入失败:', error.message);
}

try {
    const mainModule = require('./jd_lingquan.js');
    console.log('  ✅ jd_lingquan.js 导入成功');
} catch (error) {
    console.log('  ❌ jd_lingquan.js 导入失败:', error.message);
}

// 测试4: 依赖检查
console.log('\n📋 测试4: 依赖检查');
try {
    require('axios');
    console.log('  ✅ axios 依赖已安装');
} catch (error) {
    console.log('  ❌ axios 依赖未安装，请运行: npm install axios');
}

// 测试5: h5st服务连接测试（模拟）
console.log('\n📋 测试5: h5st服务配置');
const h5stUrl = 'http://10.0.0.189:3001/h5st';
console.log(`  配置的h5st服务地址: ${h5stUrl}`);
console.log('  ⚠️  请确保h5st服务正常运行，否则脚本无法正常工作');

// 测试6: 环境变量测试
console.log('\n📋 测试6: 环境变量支持');
const originalEnv = process.env.JD_COOKIE;
process.env.JD_COOKIE = 'pt_key=env_test;pt_pin=env_user;&pt_key=env_test2;pt_pin=env_user2;';

// 重新加载模块来测试环境变量
delete require.cache[require.resolve('./jdCookie.js')];
const { CookieJDs: envCookies } = require('./jdCookie.js');
console.log(`  环境变量测试: 加载了 ${envCookies.length} 个账号`);

// 恢复原始环境变量
if (originalEnv) {
    process.env.JD_COOKIE = originalEnv;
} else {
    delete process.env.JD_COOKIE;
}

console.log('\n🎉 测试完成！');
console.log('\n📝 使用说明:');
console.log('1. 确保已安装axios依赖: npm install axios');
console.log('2. 配置您的Cookie到jdCookie.js文件中');
console.log('3. 确保h5st服务正常运行');
console.log('4. 运行脚本: node jd_lingquan.js');
console.log('\n⚠️  重要提醒:');
console.log('- 请使用真实有效的京东Cookie');
console.log('- 确保h5st签名服务可用');
console.log('- 遵守京东平台使用规则');
