/*
京东领券中心抽奖脚本
活动入口：京东APP -> 领券中心 -> 外卖抽奖
功能：自动领取外卖优惠券
更新时间：2025-01-30
作者：基于抓包分析开发
*/

const { CookieJDs, extractPin, validateCookie } = require('./jdCookie.js');
const { loadActivityData, receiveReward } = require('./jd_lingquan_basic.js');

const $ = {
    name: '京东领券中心抽奖',
    version: '1.0.0',
    log: console.log,
    wait: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};

// 主执行函数
async function main() {
    console.log(`\n🎯 ========== ${$.name} ==========\n`);
    
    if (CookieJDs.length === 0) {
        console.log('❌ 未找到有效的Cookie，请检查jdCookie.js文件或环境变量JD_COOKIE');
        console.log('📝 Cookie格式示例: pt_key=xxx;pt_pin=xxx;');
        return;
    }

    console.log(`📱 共找到 ${CookieJDs.length} 个账号\n`);

    for (let i = 0; i < CookieJDs.length; i++) {
        const cookie = CookieJDs[i];
        
        if (!validateCookie(cookie)) {
            console.log(`❌ 账号${i + 1}: Cookie格式错误，跳过`);
            continue;
        }

        const pin = extractPin(cookie);
        console.log(`\n🔄 ========== 账号${i + 1}: ${pin} ==========`);

        try {
            await runAccount(cookie, pin, i + 1);
        } catch (error) {
            console.log(`❌ 账号${i + 1} 执行异常: ${error.message}`);
        }

        // 账号间延迟
        if (i < CookieJDs.length - 1) {
            console.log(`⏳ 等待3秒后执行下一个账号...`);
            await $.wait(3000);
        }
    }

    console.log(`\n🎉 ========== 所有账号执行完毕 ==========`);
}

// 单账号执行逻辑
async function runAccount(cookie, pin, accountIndex) {
    try {
        // 1. 加载活动数据
        console.log('📥 正在获取活动信息...');
        const activityData = await loadActivityData(cookie, pin);
        
        if (!activityData) {
            console.log('❌ 获取活动数据失败');
            return;
        }

        console.log(`✅ 活动: ${activityData.activityName}`);
        
        if (!activityData.rewardReceiveKey) {
            console.log('⚠️  暂无可领取的奖励或已达今日上限');
            return;
        }

        // 2. 领取奖励
        console.log('🎁 正在尝试领取奖励...');
        await $.wait(1000); // 稍作延迟
        
        const result = await receiveReward(cookie, pin, activityData);
        
        if (result.success) {
            console.log(`🎉 领取成功！`);
            console.log(`💰 获得奖励: ${result.couponDiscount}元${result.couponLimitStr}`);
            console.log(`📋 使用条件: 满${result.couponQuota}元可用`);
        } else {
            console.log(`❌ 领取失败: ${result.message}`);
            
            // 常见错误提示
            if (result.message.includes('已领取') || result.message.includes('已参与')) {
                console.log('💡 提示: 今日已参与过活动');
            } else if (result.message.includes('活动未开始') || result.message.includes('活动已结束')) {
                console.log('💡 提示: 活动时间未到或已结束');
            } else if (result.message.includes('登录')) {
                console.log('💡 提示: Cookie可能已失效，请更新');
            }
        }

    } catch (error) {
        console.log(`❌ 账号执行出错: ${error.message}`);
    }
}

// 检查依赖
function checkDependencies() {
    try {
        require('axios');
        return true;
    } catch (error) {
        console.log('❌ 缺少依赖包，请先安装:');
        console.log('npm install axios');
        return false;
    }
}

// 脚本入口
if (require.main === module) {
    if (!checkDependencies()) {
        process.exit(1);
    }
    
    main().catch(error => {
        console.error('❌ 脚本执行出错:', error);
        process.exit(1);
    });
}

module.exports = main;
