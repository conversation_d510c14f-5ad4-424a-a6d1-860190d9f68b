// 京东Cookie配置文件
// 支持多账号，每行一个完整的cookie

let CookieJDs = [
    // 示例格式，请替换为您的实际cookie
    // 'pt_key=app_openAAJoQ50VADDkgN-IltIUwZUk6F6Mh41i6m5g1vfMBYEmXjW2GMgocDmgeuqFfTWSIJQuXpCC77Y;pt_pin=jd_4b25e12eb2177;',

    // 在这里添加您的cookie，格式如上

];

// 从环境变量读取cookie（支持多账号，用&分隔）
if (process.env.JD_COOKIE) {
    const envCookies = process.env.JD_COOKIE.split('&').filter(item => item);
    CookieJDs.push(...envCookies);
}

// 过滤空cookie
CookieJDs = CookieJDs.filter(item => item && item.includes('pt_key') && item.includes('pt_pin'));

// 从cookie中提取pt_pin
function extractPin(cookie) {
    const match = cookie.match(/pt_pin=([^;]+)/);
    return match ? match[1] : '';
}

// 验证cookie格式
function validateCookie(cookie) {
    return cookie && cookie.includes('pt_key=') && cookie.includes('pt_pin=');
}

// 只在直接运行时输出信息
if (require.main === module) {
    console.log(`✅ 共加载 ${CookieJDs.length} 个有效账号`);
}

module.exports = {
    CookieJDs,
    extractPin,
    validateCookie
};
