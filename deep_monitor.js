/*
深度网络监控脚本
使用多种方法监控dy.js的网络请求
*/

const Module = require('module');
const originalRequire = Module.prototype.require;

// 存储捕获的请求
let capturedRequests = [];
let requestId = 0;

// 拦截require调用
Module.prototype.require = function(id) {
    const module = originalRequire.apply(this, arguments);
    
    // 拦截axios
    if (id === 'axios' || (module && module.default && typeof module.default === 'function')) {
        console.log('🔍 检测到axios模块加载');
        return wrapAxios(module);
    }
    
    // 拦截request
    if (id === 'request' || id === 'request-promise' || id === 'request-promise-native') {
        console.log('🔍 检测到request模块加载');
        return wrapRequest(module);
    }
    
    // 拦截fetch
    if (id === 'node-fetch' || id === 'fetch') {
        console.log('🔍 检测到fetch模块加载');
        return wrapFetch(module);
    }
    
    return module;
};

// 包装axios
function wrapAxios(axios) {
    if (!axios) return axios;
    
    const originalAxios = axios.default || axios;
    
    function wrappedAxios(config) {
        console.log('🌐 [AXIOS] 请求拦截:', config);
        logRequest('AXIOS', config);
        
        const result = originalAxios.apply(this, arguments);
        
        if (result && result.then) {
            return result.then(response => {
                console.log('📥 [AXIOS] 响应:', response.status, response.data);
                return response;
            }).catch(error => {
                console.log('❌ [AXIOS] 错误:', error.message);
                throw error;
            });
        }
        
        return result;
    }
    
    // 复制原始axios的属性
    Object.keys(originalAxios).forEach(key => {
        wrappedAxios[key] = originalAxios[key];
    });
    
    // 包装常用方法
    ['get', 'post', 'put', 'delete', 'patch'].forEach(method => {
        if (originalAxios[method]) {
            wrappedAxios[method] = function(...args) {
                console.log(`🌐 [AXIOS.${method.toUpperCase()}]`, args[0]);
                logRequest(`AXIOS.${method.toUpperCase()}`, { url: args[0], data: args[1] });
                
                const result = originalAxios[method].apply(this, arguments);
                
                if (result && result.then) {
                    return result.then(response => {
                        console.log(`📥 [AXIOS.${method.toUpperCase()}] 响应:`, response.status);
                        return response;
                    }).catch(error => {
                        console.log(`❌ [AXIOS.${method.toUpperCase()}] 错误:`, error.message);
                        throw error;
                    });
                }
                
                return result;
            };
        }
    });
    
    if (axios.default) {
        return { ...axios, default: wrappedAxios };
    } else {
        return wrappedAxios;
    }
}

// 包装request
function wrapRequest(request) {
    if (!request) return request;
    
    return function wrappedRequest(options, callback) {
        console.log('🌐 [REQUEST] 请求拦截:', options);
        logRequest('REQUEST', options);
        
        if (callback) {
            const wrappedCallback = function(error, response, body) {
                if (error) {
                    console.log('❌ [REQUEST] 错误:', error.message);
                } else {
                    console.log('📥 [REQUEST] 响应:', response.statusCode);
                }
                callback(error, response, body);
            };
            
            return request(options, wrappedCallback);
        } else {
            const result = request(options);
            if (result && result.then) {
                return result.then(response => {
                    console.log('📥 [REQUEST] 响应:', response.statusCode);
                    return response;
                }).catch(error => {
                    console.log('❌ [REQUEST] 错误:', error.message);
                    throw error;
                });
            }
            return result;
        }
    };
}

// 包装fetch
function wrapFetch(fetch) {
    if (!fetch) return fetch;
    
    return function wrappedFetch(url, options) {
        console.log('🌐 [FETCH] 请求拦截:', url, options);
        logRequest('FETCH', { url, ...options });
        
        const result = fetch(url, options);
        
        if (result && result.then) {
            return result.then(response => {
                console.log('📥 [FETCH] 响应:', response.status);
                return response;
            }).catch(error => {
                console.log('❌ [FETCH] 错误:', error.message);
                throw error;
            });
        }
        
        return result;
    };
}

// 记录请求
function logRequest(type, config) {
    requestId++;
    const request = {
        id: requestId,
        type: type,
        timestamp: new Date().toISOString(),
        config: config
    };
    
    capturedRequests.push(request);
    
    console.log(`📋 [${requestId}] ${type} 请求记录:`, JSON.stringify(config, null, 2));
}

// 拦截全局的XMLHttpRequest（如果有的话）
if (global.XMLHttpRequest) {
    const OriginalXHR = global.XMLHttpRequest;
    global.XMLHttpRequest = function() {
        const xhr = new OriginalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        
        xhr.open = function(method, url, ...args) {
            console.log('🌐 [XHR] 请求拦截:', method, url);
            logRequest('XHR', { method, url });
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        xhr.send = function(data) {
            console.log('📤 [XHR] 发送数据:', data);
            return originalSend.apply(this, [data]);
        };
        
        return xhr;
    };
}

console.log('🚀 深度网络监控已启动...\n');

// 运行dy.js
const { spawn } = require('child_process');

const dyProcess = spawn('node', ['dy.js'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    env: process.env
});

dyProcess.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(`[dy.js] ${output}`);
    
    // 检查输出中是否包含网络请求相关信息
    if (output.includes('http') || output.includes('api') || output.includes('request')) {
        console.log('🔍 检测到可能的网络请求信息:', output);
    }
});

dyProcess.stderr.on('data', (data) => {
    console.error(`[dy.js错误] ${data.toString()}`);
});

dyProcess.on('close', (code) => {
    console.log(`\n🏁 dy.js进程结束，退出码: ${code}`);
    console.log(`📊 总共捕获了 ${capturedRequests.length} 个网络请求\n`);
    
    if (capturedRequests.length > 0) {
        console.log('📋 捕获的请求详情:');
        capturedRequests.forEach((req, index) => {
            console.log(`\n[${index + 1}] ${req.type} - ${req.timestamp}`);
            console.log('配置:', JSON.stringify(req.config, null, 2));
        });
        
        // 保存到文件
        const fs = require('fs');
        const reportFile = `dy_deep_requests_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(capturedRequests, null, 2));
        console.log(`\n💾 详细报告已保存到: ${reportFile}`);
    } else {
        console.log('❌ 未捕获到任何网络请求');
        console.log('💡 可能的原因:');
        console.log('  1. dy.js使用了自定义的网络请求库');
        console.log('  2. 网络请求被其他方式封装');
        console.log('  3. 脚本在网络请求前就出错了');
    }
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n⏹️  监控被中断');
    dyProcess.kill();
    process.exit(0);
});
