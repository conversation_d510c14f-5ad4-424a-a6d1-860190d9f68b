/*
使用示例文件
演示如何使用京东领券中心抽奖脚本
*/

// 示例1: 直接调用基础功能
async function example1() {
    const { loadActivityData, receiveReward } = require('./jd_lingquan_basic.js');
    
    // 替换为您的实际cookie和pin
    const cookie = 'pt_key=your_pt_key;pt_pin=your_pt_pin;';
    const pin = 'your_pt_pin';
    
    console.log('示例1: 单次执行');
    
    // 加载活动数据
    const activityData = await loadActivityData(cookie, pin);
    if (activityData) {
        console.log('活动数据:', activityData);
        
        // 领取奖励
        const result = await receiveReward(cookie, pin, activityData);
        console.log('领取结果:', result);
    }
}

// 示例2: 使用环境变量
async function example2() {
    // 设置环境变量
    process.env.JD_COOKIE = 'pt_key=your_pt_key;pt_pin=your_pt_pin;';
    
    // 运行主脚本
    const main = require('./jd_lingquan.js');
    await main();
}

// 示例3: 批量处理多个账号
async function example3() {
    const { CookieJDs, extractPin } = require('./jdCookie.js');
    const { loadActivityData, receiveReward } = require('./jd_lingquan_basic.js');
    
    // 手动添加多个cookie
    const cookies = [
        'pt_key=key1;pt_pin=pin1;',
        'pt_key=key2;pt_pin=pin2;',
        // 更多账号...
    ];
    
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i];
        const pin = extractPin(cookie);
        
        console.log(`处理账号 ${i + 1}: ${pin}`);
        
        try {
            const activityData = await loadActivityData(cookie, pin);
            if (activityData) {
                const result = await receiveReward(cookie, pin, activityData);
                console.log(`账号 ${i + 1} 结果:`, result);
            }
        } catch (error) {
            console.log(`账号 ${i + 1} 出错:`, error.message);
        }
        
        // 延迟
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
}

// 运行示例（取消注释来测试）
// example1();
// example2();
// example3();

console.log('请根据需要取消注释相应的示例函数来测试');
console.log('记得替换示例中的cookie为您的真实cookie');
