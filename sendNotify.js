/*
简单的通知发送模块
用于替代缺失的sendNotify依赖
*/

// 模拟发送通知的函数
async function sendNotify(title, content, options = {}) {
    console.log(`\n📢 通知标题: ${title}`);
    console.log(`📝 通知内容: ${content}`);
    
    // 模拟异步操作
    return new Promise((resolve) => {
        setTimeout(() => {
            console.log('✅ 通知发送完成');
            resolve(true);
        }, 100);
    });
}

// 导出函数
module.exports = {
    sendNotify: sendNotify,
    default: sendNotify
};

// 兼容不同的导入方式
module.exports.sendNotify = sendNotify;
