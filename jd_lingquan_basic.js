const axios = require('axios');

// 基础配置
const config = {
    h5stUrl: 'http://**********:3001/h5st',
    userAgent: 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;'
};

// 原始h5st值（从抓包中提取）
const ORIGINAL_H5ST = {
    comp_data_load: '20250602193855954%3Bgzixaww9w3w3amw4%3Bec373%3Btk05w204358a341lMyszWTliNTJWsmuh35YW7S3i3J7R3poQFF3VuueuXs7Ty-4h4eIaBeqg8GadiZbV9WIWIhYWKtrV%3B74fc73bf10b7da690dd855061580f106a32caaeb8731f4f77957d799cc9119f1%3B5.1%3B1748864334954%3BsmePkmsh35YW7S3i3J7R3poQFF3VMuMgMuHVMusmk_Mm1qIhIlbhJZIhIhLh2m7hJZYV5WLh4eIW_m7W7aIWLtLmOGLm_VqTHlYV3lsmOGujMabW7ioiJpoiLtbiJJ7i7WLW6aoi4iIiLtLiIt7iLVYhMuMgMiXW41YWLlsmOGuj96sm0msh5lImOuMsCmshAqLj5W3XJ9YUIxZhGlsm0mMRMusmk_MmbJZiEh6iexKilZqcnlsm0mcT-dITNlHmOuMsCmcVAxoUeJImOGLmItHmOuMsC6nmOGOiOGLm9qbRMlsmOusmk_ci9uMgMubi5lImOusmOGuj26sm0mMi9aHWMusmOuMsCm8dWpYd96qiWlrdU9IYMuMgM64TK1YW8lsmOusmk_siOGLm2aHWMusmOuMsCurm0m8h5lImOusmOGuj9irm0mMh5lImOusmOGuj_uMgMabRMlsmOusmk_siOGLm6aHWMusmOuMsCm7hOGLm7aHWMusmOuMsCmchAqLj_yZV6JoTMuMgMqbRMlsmOusmk_siOGLmDRHmOusmOGuj96sm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPOuMsMS7h9mLWJlbiJZLiMd7XKFImOGLm9uHmOusmOG_QOGLm_tHmOuMsCmsYOi5bOiYWhtcVDJoTOq7X6qrmbxqmJ14TGtZUOapart8gJ14TGtZUMuMgMqYR7lsmOG_Q%3Bd1d935e7f82d3986883b8f1b8b21e4029a9f48dd9ae09b8d0d5b691d4197e609%3Bri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW',
    comp_data_interact: '20250602193856941%3Baixipa3axq30h2w9%3B93453%3Btk03w8a3b1bc918natIf0E29uONqJ5ocYU1pPM6VE0PRy_jt2dvpB-1e3_5ZIQWEQwtcAwwONZR1hzSPVH7RCavwqg0V%3B6577523268131c9924328bc1545d0a1eee06ae6a48f982160b431d3677e033cb%3B5.1%3B1748864335941%3BsmePkmcg3lrU_ibS2p4iNtXU2JYWMuMgMuHVMusmk_Mm1qIhIlbhJZIhIhLh2m7hJZYV5WLh4eIW_m7W7aIWLtLmOGLm_VqTHlYV3lsmOGujMabW7ioiJpoiLtbiJJ7i7WLW6aoi4iIiLtLiIt7iLVYhMuMgMiXW41YWLlsmOGuj96sm0msh5lImOuMsCmshAqLj5W3XJ9YUIxZhGlsm0mMRMusmk_Mm9G6Tn54i_OLinBai3msm0mcT-dITNlHmOuMsCmcVAxoUeJImOGLmItHmOuMsC6nmOGOiOGLm9qbRMlsmOusmk_ci9uMgMubi5lImOusmOGuj26sm0mMi9aHWMusmOuMsCmcWmxZbHVJTElbYmJZUMuMgM64TK1YW8lsmOusmk_siOGLm2aHWMusmOuMsCurm0m8h5lImOusmOGuj9irm0mMh5lImOusmOGuj_uMgMabRMlsmOusmk_siOGLm6aHWMusmOuMsCm7hOGLm7aHWMusmOuMsCmchAqLj_yZV6JoTMuMgMqbRMlsmOusmk_siOGLmDRHmOusmOGuj96sm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPOuMsMS7h9mLWJlbiJZLiMd7XKFImOGLm9uHmOusmOG_QOGLm_tHmOuMsCmsYOi5bOiYWhtcVDJoTOq7X6qrmbxqmJ14TGtZUOapart8gJ14TGtZUMuMgMqYR7lsmOG_Q%3Bc42983c57092a7ec97d8052d4c8d06ee092b9763eb1c20d323e828fe90cd17f7%3Bri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW'
};

// 获取h5st签名
async function getH5st(functionId, body, pin) {
    try {
        // 获取对应的原始h5st值
        const originalH5st = ORIGINAL_H5ST[functionId] || '';

        const h5stParams = {
            appid: 'day_day_reward',  // 添加appid参数
            version: '5.1',
            functionId: functionId,
            pin: pin,
            ua: config.userAgent,
            h5st: originalH5st,  // 使用原始h5st值
            body: {
                token: body.token || '',
                fnCode: body.fnCode || '',
                commParams: body.commParams || {},
                bizParams: body.bizParams || {}
            }
        };

        const response = await axios.post(config.h5stUrl, h5stParams, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.data && response.data.h5st) {
            return response.data.h5st;
        }
        throw new Error('获取h5st失败');
    } catch (error) {
        console.log('❌ h5st签名失败:', error.message);
        return null;
    }
}

// 加载活动数据
async function loadActivityData(cookie, pin) {
    try {
        const body = {
            token: '',
            commParams: {
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                openChannel: 'jdAppHome',
                actKey: 'iadem13cpdiof3vtyykmr',
                subLabel: ''
            }
        };

        const h5st = await getH5st('comp_data_load', body, pin);
        if (!h5st) {
            throw new Error('获取h5st签名失败');
        }

        const params = new URLSearchParams({
            appid: 'day_day_reward',
            functionId: 'comp_data_load',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(body),
            h5st: h5st,
            'x-api-eid-token': 'jdd03IFRDI7JL55ZJCFWHCCFV35GLGY4BZH4LAVWYCMXUV2W6AU3YZZ5TWUYNRD565WJQS4XHZWCHFZUN5WOPAAU7UVYUZMAAAAMXGADY2BQAAAAACOADRLXEMVDGZIX'
        });

        const response = await axios.post('https://api.m.jd.com/client.action', params.toString(), {
            headers: {
                'Host': 'api.m.jd.com',
                'Accept': '*/*',
                'x-rp-client': 'h5_1.0.0',
                'Accept-Language': 'zh-cn',
                'Accept-Encoding': 'gzip, deflate, br',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://pro.m.jd.com',
                'User-Agent': config.userAgent,
                'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'Connection': 'keep-alive',
                'Cookie': cookie
            }
        });

        if (response.data && response.data.success) {
            const data = response.data.data;
            return {
                token: response.data.token,
                actKey: data.actBasicInfo?.actEncKey || 'iadem13cpdiof3vtyykmr',
                rewardReceiveKey: data.rewardProgressItems?.[0]?.rewardReceiveKey || '',
                activityName: data.actBasicInfo?.name || '未知活动'
            };
        }
        throw new Error(response.data?.message || '加载活动数据失败');
    } catch (error) {
        console.log('❌ 加载活动数据失败:', error.message);
        return null;
    }
}

// 领取奖励
async function receiveReward(cookie, pin, activityData) {
    try {
        const body = {
            token: activityData.token,
            fnCode: 'invoke',
            commParams: {
                longitude: '113.328752',
                latitude: '23.17921',
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                rewardReceiveKey: activityData.rewardReceiveKey,
                openChannel: 'jdAppHome',
                actFlowCode: 'receiveReward',
                actKey: activityData.actKey,
                subLabel: ''
            }
        };

        const h5st = await getH5st('comp_data_interact', body, pin);
        if (!h5st) {
            throw new Error('获取h5st签名失败');
        }

        const params = new URLSearchParams({
            appid: 'day_day_reward',
            functionId: 'comp_data_interact',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(body),
            h5st: h5st,
            'x-api-eid-token': 'jdd03IFRDI7JL55ZJCFWHCCFV35GLGY4BZH4LAVWYCMXUV2W6AU3YZZ5TWUYNRD565WJQS4XHZWCHFZUN5WOPAAU7UVYUZMAAAAMXGADY2BQAAAAACOADRLXEMVDGZIX'
        });

        const response = await axios.post('https://api.m.jd.com/client.action', params.toString(), {
            headers: {
                'Host': 'api.m.jd.com',
                'Accept': '*/*',
                'x-rp-client': 'h5_1.0.0',
                'Accept-Language': 'zh-cn',
                'Accept-Encoding': 'gzip, deflate, br',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://pro.m.jd.com',
                'User-Agent': config.userAgent,
                'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'Connection': 'keep-alive',
                'Cookie': cookie
            }
        });

        if (response.data && response.data.success) {
            const rewardInfo = response.data.data?.rewardInfoList?.[0];
            if (rewardInfo) {
                const couponInfo = rewardInfo.couponInfo;
                return {
                    success: true,
                    couponDiscount: couponInfo?.couponDiscount || 0,
                    couponQuota: couponInfo?.couponQuota || 0,
                    couponLimitStr: couponInfo?.couponLimitStr || '未知券',
                    message: '领取成功'
                };
            }
        }
        throw new Error(response.data?.message || '领取失败');
    } catch (error) {
        console.log('❌ 领取奖励失败:', error.message);
        return { success: false, message: error.message };
    }
}

// 主函数
async function main() {
    // 这里需要替换为实际的cookie
    const cookie = 'pt_key=your_pt_key;pt_pin=your_pt_pin;';
    const pin = 'your_pin'; // 从cookie中提取的pt_pin值

    console.log('🚀 开始执行京东领券中心抽奖...');
    
    // 1. 加载活动数据
    console.log('📥 正在加载活动数据...');
    const activityData = await loadActivityData(cookie, pin);
    if (!activityData) {
        console.log('❌ 获取活动数据失败，程序退出');
        return;
    }
    
    console.log(`✅ 活动数据加载成功: ${activityData.activityName}`);
    console.log(`🎯 Token: ${activityData.token}`);
    console.log(`🔑 ActKey: ${activityData.actKey}`);
    
    // 2. 领取奖励
    console.log('🎁 正在领取奖励...');
    const result = await receiveReward(cookie, pin, activityData);
    
    if (result.success) {
        console.log(`🎉 ${result.message}`);
        console.log(`💰 获得: ${result.couponDiscount}元${result.couponLimitStr} (满${result.couponQuota}元可用)`);
    } else {
        console.log(`❌ ${result.message}`);
    }
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, loadActivityData, receiveReward };
