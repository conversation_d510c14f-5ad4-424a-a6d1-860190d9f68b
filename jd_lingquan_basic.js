const axios = require('axios');

// 基础配置
const config = {
    h5stUrl: 'http://**********:3001/h5st',
    userAgent: 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;'
};

// 获取h5st签名
async function getH5st(functionId, body, pin) {
    try {
        const h5stParams = {
            version: '5.1',
            functionId: functionId,
            pin: pin,
            ua: config.userAgent,
            h5st: '',
            body: {
                token: body.token || '',
                fnCode: body.fnCode || '',
                commParams: body.commParams || {},
                bizParams: body.bizParams || {}
            }
        };

        const response = await axios.post(config.h5stUrl, h5stParams, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.data && response.data.h5st) {
            return response.data.h5st;
        }
        throw new Error('获取h5st失败');
    } catch (error) {
        console.log('❌ h5st签名失败:', error.message);
        return null;
    }
}

// 加载活动数据
async function loadActivityData(cookie, pin) {
    try {
        const body = {
            token: '',
            commParams: {
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                openChannel: 'jdAppHome',
                actKey: 'iadem13cpdiof3vtyykmr',
                subLabel: ''
            }
        };

        const h5st = await getH5st('comp_data_load', body, pin);
        if (!h5st) {
            throw new Error('获取h5st签名失败');
        }

        const params = new URLSearchParams({
            appid: 'day_day_reward',
            functionId: 'comp_data_load',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(body),
            h5st: h5st,
            'x-api-eid-token': 'jdd03IFRDI7JL55ZJCFWHCCFV35GLGY4BZH4LAVWYCMXUV2W6AU3YZZ5TWUYNRD565WJQS4XHZWCHFZUN5WOPAAU7UVYUZMAAAAMXGADY2BQAAAAACOADRLXEMVDGZIX'
        });

        const response = await axios.post('https://api.m.jd.com/client.action', params.toString(), {
            headers: {
                'Host': 'api.m.jd.com',
                'Accept': '*/*',
                'x-rp-client': 'h5_1.0.0',
                'Accept-Language': 'zh-cn',
                'Accept-Encoding': 'gzip, deflate, br',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://pro.m.jd.com',
                'User-Agent': config.userAgent,
                'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'Connection': 'keep-alive',
                'Cookie': cookie
            }
        });

        if (response.data && response.data.success) {
            const data = response.data.data;
            return {
                token: response.data.token,
                actKey: data.actBasicInfo?.actEncKey || 'iadem13cpdiof3vtyykmr',
                rewardReceiveKey: data.rewardProgressItems?.[0]?.rewardReceiveKey || '',
                activityName: data.actBasicInfo?.name || '未知活动'
            };
        }
        throw new Error(response.data?.message || '加载活动数据失败');
    } catch (error) {
        console.log('❌ 加载活动数据失败:', error.message);
        return null;
    }
}

// 领取奖励
async function receiveReward(cookie, pin, activityData) {
    try {
        const body = {
            token: activityData.token,
            fnCode: 'invoke',
            commParams: {
                longitude: '113.328752',
                latitude: '23.17921',
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                rewardReceiveKey: activityData.rewardReceiveKey,
                openChannel: 'jdAppHome',
                actFlowCode: 'receiveReward',
                actKey: activityData.actKey,
                subLabel: ''
            }
        };

        const h5st = await getH5st('comp_data_interact', body, pin);
        if (!h5st) {
            throw new Error('获取h5st签名失败');
        }

        const params = new URLSearchParams({
            appid: 'day_day_reward',
            functionId: 'comp_data_interact',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(body),
            h5st: h5st,
            'x-api-eid-token': 'jdd03IFRDI7JL55ZJCFWHCCFV35GLGY4BZH4LAVWYCMXUV2W6AU3YZZ5TWUYNRD565WJQS4XHZWCHFZUN5WOPAAU7UVYUZMAAAAMXGADY2BQAAAAACOADRLXEMVDGZIX'
        });

        const response = await axios.post('https://api.m.jd.com/client.action', params.toString(), {
            headers: {
                'Host': 'api.m.jd.com',
                'Accept': '*/*',
                'x-rp-client': 'h5_1.0.0',
                'Accept-Language': 'zh-cn',
                'Accept-Encoding': 'gzip, deflate, br',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://pro.m.jd.com',
                'User-Agent': config.userAgent,
                'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
                'Connection': 'keep-alive',
                'Cookie': cookie
            }
        });

        if (response.data && response.data.success) {
            const rewardInfo = response.data.data?.rewardInfoList?.[0];
            if (rewardInfo) {
                const couponInfo = rewardInfo.couponInfo;
                return {
                    success: true,
                    couponDiscount: couponInfo?.couponDiscount || 0,
                    couponQuota: couponInfo?.couponQuota || 0,
                    couponLimitStr: couponInfo?.couponLimitStr || '未知券',
                    message: '领取成功'
                };
            }
        }
        throw new Error(response.data?.message || '领取失败');
    } catch (error) {
        console.log('❌ 领取奖励失败:', error.message);
        return { success: false, message: error.message };
    }
}

// 主函数
async function main() {
    // 这里需要替换为实际的cookie
    const cookie = 'pt_key=your_pt_key;pt_pin=your_pt_pin;';
    const pin = 'your_pin'; // 从cookie中提取的pt_pin值

    console.log('🚀 开始执行京东领券中心抽奖...');
    
    // 1. 加载活动数据
    console.log('📥 正在加载活动数据...');
    const activityData = await loadActivityData(cookie, pin);
    if (!activityData) {
        console.log('❌ 获取活动数据失败，程序退出');
        return;
    }
    
    console.log(`✅ 活动数据加载成功: ${activityData.activityName}`);
    console.log(`🎯 Token: ${activityData.token}`);
    console.log(`🔑 ActKey: ${activityData.actKey}`);
    
    // 2. 领取奖励
    console.log('🎁 正在领取奖励...');
    const result = await receiveReward(cookie, pin, activityData);
    
    if (result.success) {
        console.log(`🎉 ${result.message}`);
        console.log(`💰 获得: ${result.couponDiscount}元${result.couponLimitStr} (满${result.couponQuota}元可用)`);
    } else {
        console.log(`❌ ${result.message}`);
    }
}

// 执行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, loadActivityData, receiveReward };
