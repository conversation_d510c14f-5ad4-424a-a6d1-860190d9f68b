/*
h5st调试工具
用于测试和调试h5st服务的响应格式
*/

const axios = require('axios');

// 配置
const config = {
    h5stUrl: 'http://**********:3001/h5st',
    userAgent: 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;'
};

// 原始h5st值
const ORIGINAL_H5ST = {
    comp_data_load: '20250602193855954%3Bgzixaww9w3w3amw4%3Bec373%3Btk05w204358a341lMyszWTliNTJWsmuh35YW7S3i3J7R3poQFF3VuueuXs7Ty-4h4eIaBeqg8GadiZbV9WIWIhYWKtrV%3B74fc73bf10b7da690dd855061580f106a32caaeb8731f4f77957d799cc9119f1%3B5.1%3B1748864334954%3BsmePkmsh35YW7S3i3J7R3poQFF3VMuMgMuHVMusmk_Mm1qIhIlbhJZIhIhLh2m7hJZYV5WLh4eIW_m7W7aIWLtLmOGLm_VqTHlYV3lsmOGujMabW7ioiJpoiLtbiJJ7i7WLW6aoi4iIiLtLiIt7iLVYhMuMgMiXW41YWLlsmOGuj96sm0msh5lImOuMsCmshAqLj5W3XJ9YUIxZhGlsm0mMRMusmk_MmbJZiEh6iexKilZqcnlsm0mcT-dITNlHmOuMsCmcVAxoUeJImOGLmItHmOuMsC6nmOGOiOGLm9qbRMlsmOusmk_ci9uMgMubi5lImOusmOGuj26sm0mMi9aHWMusmOuMsCm8dWpYd96qiWlrdU9IYMuMgM64TK1YW8lsmOusmk_siOGLm2aHWMusmOuMsCurm0m8h5lImOusmOGuj9irm0mMh5lImOusmOGuj_uMgMabRMlsmOusmk_siOGLm6aHWMusmOuMsCm7hOGLm7aHWMusmOuMsCmchAqLj_yZV6JoTMuMgMqbRMlsmOusmk_siOGLmDRHmOusmOGuj96sm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPOuMsMS7h9mLWJlbiJZLiMd7XKFImOGLm9uHmOusmOG_QOGLm_tHmOuMsCmsYOi5bOiYWhtcVDJoTOq7X6qrmbxqmJ14TGtZUOapart8gJ14TGtZUMuMgMqYR7lsmOG_Q%3Bd1d935e7f82d3986883b8f1b8b21e4029a9f48dd9ae09b8d0d5b691d4197e609%3Bri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW',
    comp_data_interact: '20250602193856941%3Baixipa3axq30h2w9%3B93453%3Btk03w8a3b1bc918natIf0E29uONqJ5ocYU1pPM6VE0PRy_jt2dvpB-1e3_5ZIQWEQwtcAwwONZR1hzSPVH7RCavwqg0V%3B6577523268131c9924328bc1545d0a1eee06ae6a48f982160b431d3677e033cb%3B5.1%3B1748864335941%3BsmePkmcg3lrU_ibS2p4iNtXU2JYWMuMgMuHVMusmk_Mm1qIhIlbhJZIhIhLh2m7hJZYV5WLh4eIW_m7W7aIWLtLmOGLm_VqTHlYV3lsmOGujMabW7ioiJpoiLtbiJJ7i7WLW6aoi4iIiLtLiIt7iLVYhMuMgMiXW41YWLlsmOGuj96sm0msh5lImOuMsCmshAqLj5W3XJ9YUIxZhGlsm0mMRMusmk_Mm9G6Tn54i_OLinBai3msm0mcT-dITNlHmOuMsCmcVAxoUeJImOGLmItHmOuMsC6nmOGOiOGLm9qbRMlsmOusmk_ci9uMgMubi5lImOusmOGuj26sm0mMi9aHWMusmOuMsCmcWmxZbHVJTElbYmJZUMuMgM64TK1YW8lsmOusmk_siOGLm2aHWMusmOuMsCurm0m8h5lImOusmOGuj9irm0mMh5lImOusmOGuj_uMgMabRMlsmOusmk_siOGLm6aHWMusmOuMsCm7hOGLm7aHWMusmOuMsCmchAqLj_yZV6JoTMuMgMqbRMlsmOusmk_siOGLmDRHmOusmOGuj96sm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPOuMsMS7h9mLWJlbiJZLiMd7XKFImOGLm9uHmOusmOG_QOGLm_tHmOuMsCmsYOi5bOiYWhtcVDJoTOq7X6qrmbxqmJ14TGtZUOapart8gJ14TGtZUMuMgMqYR7lsmOG_Q%3Bc42983c57092a7ec97d8052d4c8d06ee092b9763eb1c20d323e828fe90cd17f7%3Bri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW'
};

// 测试h5st请求
async function testH5stRequest(functionId) {
    console.log(`\n🧪 测试 ${functionId} 的h5st请求`);
    console.log('=' .repeat(50));
    
    try {
        const originalH5st = ORIGINAL_H5ST[functionId] || '';
        
        const h5stParams = {
            appid: 'day_day_reward',
            version: '5.1',
            functionId: functionId,
            pin: 'test_user',
            ua: config.userAgent,
            h5st: originalH5st,
            body: {
                token: 'test_token',
                fnCode: functionId === 'comp_data_interact' ? 'invoke' : '',
                commParams: {
                    ubbLoc: 'ttf.lqzx',
                    lid: '19_1601_50258_62859',
                    client: 0
                },
                bizParams: {
                    openChannel: 'jdAppHome',
                    actKey: 'test_act_key'
                }
            }
        };

        console.log('📤 发送请求参数:');
        console.log(`  - URL: ${config.h5stUrl}`);
        console.log(`  - appid: ${h5stParams.appid}`);
        console.log(`  - functionId: ${h5stParams.functionId}`);
        console.log(`  - pin: ${h5stParams.pin}`);
        console.log(`  - 原始h5st长度: ${originalH5st.length} 字符`);
        console.log(`  - body结构: ${JSON.stringify(Object.keys(h5stParams.body))}`);

        const startTime = Date.now();
        const response = await axios.post(config.h5stUrl, h5stParams, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000  // 10秒超时
        });
        const endTime = Date.now();

        console.log(`\n📥 收到响应 (耗时: ${endTime - startTime}ms):`);
        console.log(`  - HTTP状态码: ${response.status}`);
        console.log(`  - 响应头: ${JSON.stringify(response.headers['content-type'])}`);
        console.log(`  - 响应数据类型: ${typeof response.data}`);
        console.log(`  - 响应数据: ${JSON.stringify(response.data, null, 2)}`);

        // 尝试解析h5st
        let h5stValue = null;
        if (response.data) {
            if (response.data.h5st) {
                h5stValue = response.data.h5st;
                console.log('✅ 找到h5st (格式1): response.data.h5st');
            } else if (response.data.data && response.data.data.h5st) {
                h5stValue = response.data.data.h5st;
                console.log('✅ 找到h5st (格式2): response.data.data.h5st');
            } else if (response.data.result && response.data.result.h5st) {
                h5stValue = response.data.result.h5st;
                console.log('✅ 找到h5st (格式3): response.data.result.h5st');
            } else if (typeof response.data === 'string' && response.data.length > 50) {
                h5stValue = response.data;
                console.log('✅ 找到h5st (格式4): 直接字符串');
            } else if (response.data.code === 0 && response.data.data) {
                h5stValue = response.data.data;
                console.log('✅ 找到h5st (格式5): response.data.data (code=0)');
            }
        }

        if (h5stValue) {
            console.log(`🎯 解析出的h5st: ${h5stValue.substring(0, 100)}...`);
            console.log(`📏 h5st长度: ${h5stValue.length} 字符`);
            return h5stValue;
        } else {
            console.log('❌ 未能解析出h5st值');
            return null;
        }

    } catch (error) {
        console.log('❌ 请求失败:', error.message);
        
        if (error.response) {
            console.log(`📡 HTTP错误: ${error.response.status}`);
            console.log(`📡 错误响应: ${JSON.stringify(error.response.data)}`);
        } else if (error.request) {
            console.log('📡 网络错误: 无法连接到h5st服务');
            console.log('💡 请检查:');
            console.log('   1. h5st服务是否正在运行');
            console.log('   2. 服务地址是否正确');
            console.log('   3. 网络连接是否正常');
        } else {
            console.log('📡 其他错误:', error.message);
        }
        
        return null;
    }
}

// 主测试函数
async function main() {
    console.log('🚀 h5st调试工具启动');
    console.log(`🔗 测试服务地址: ${config.h5stUrl}`);
    
    // 测试连接
    console.log('\n🔍 测试服务连接...');
    try {
        const response = await axios.get(config.h5stUrl.replace('/h5st', '/'), { timeout: 5000 });
        console.log('✅ 服务连接正常');
    } catch (error) {
        console.log('❌ 服务连接失败:', error.message);
        console.log('💡 请确保h5st服务正在运行');
    }
    
    // 测试两个接口
    await testH5stRequest('comp_data_load');
    await testH5stRequest('comp_data_interact');
    
    console.log('\n🎉 调试完成！');
    console.log('\n💡 如果遇到问题，请检查:');
    console.log('1. h5st服务是否支持新的请求参数格式');
    console.log('2. 响应格式是否符合预期');
    console.log('3. 网络连接是否正常');
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testH5stRequest };
