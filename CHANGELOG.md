# 更新日志

## v1.3.0 (2025-01-30) - HAR文件分析优化

### 🔍 HAR文件分析结果

通过分析 `ProxyPin6-8_19_43_43.har` 文件发现：

#### 关键发现
- ✅ **comp_data_load位置**: 第25个请求
- ✅ **前置请求数量**: 24个请求
- ✅ **无前置获取**: 未发现获取actKey和token的前置API请求

#### 参数来源分析
- `actKey: "iadem13cpdiof3vtyykmr"` - 固定值，无需动态获取
- `token: "pVQSJCqPp3oXPfH9Y28Tv"` - 可能是固定值或从页面提取
- 前置请求主要是日志上报和基础配置，不涉及活动参数获取

#### 脚本优化建议
- 🎯 **保持当前设计**: 直接使用固定actKey
- 🎯 **简化流程**: 无需添加前置请求获取参数
- 🎯 **专注核心**: 重点优化h5st签名和token获取

### 📁 新增分析工具
- `analyze_har.py` - HAR文件分析工具
- `detailed_analysis.py` - 详细请求分析工具

---

## v1.2.0 (2025-01-30) - 动态token获取

### 🔧 主要更新

#### 动态x-api-eid-token获取
- ✅ **新增token获取服务**: 从 `http://10.0.0.111:4444/getJsToken` 动态获取token
- ✅ **自动token更新**: 每次请求都获取最新的x-api-eid-token
- ✅ **错误处理机制**: 获取失败时自动使用备用token
- ✅ **详细日志输出**: 完整的token获取过程日志

#### token服务响应格式
```json
{
  "success": true,
  "token": {
    "token": "jdd03xxxxx...",
    "eid": "xxxxx...",
    "gia_d": 1,
    "deMap": null,
    "ds": 120
  }
}
```

#### 功能特点
- 🔄 **动态生成**: 每次请求获取新的token
- 🛡️ **容错机制**: 服务异常时使用备用token
- 📊 **成功率监控**: 详细的获取成功率统计
- 🔍 **调试支持**: 完整的测试和调试工具

### 📁 新增文件
- `test_token.js` - token获取功能测试工具

### 🔧 修改文件
- `jd_lingquan_basic.js` - 集成动态token获取
- `README.md` - 更新token获取说明
- `CHANGELOG.md` - 记录更新内容

### 🚀 使用方法
1. **确保token服务运行**: `http://10.0.0.111:4444/getJsToken`
2. **测试token获取**: `node test_token.js`
3. **正常运行脚本**: `node jd_lingquan.js`

---

## v1.1.0 (2025-01-30) - h5st参数优化

### 🔧 主要更新

#### h5st请求参数优化
- ✅ **新增 `appid` 参数**: 所有h5st请求现在都包含 `appid: "day_day_reward"`
- ✅ **集成原始h5st值**: 从抓包数据中提取的原始h5st值作为参考参数
- ✅ **支持多种响应格式**: 兼容不同h5st服务的响应结构

#### 新的h5st请求格式
```json
{
  "appid": "day_day_reward",
  "version": "5.1", 
  "functionId": "comp_data_load | comp_data_interact",
  "pin": "用户pin",
  "ua": "用户代理字符串",
  "h5st": "原始抓包的h5st值",
  "body": {
    "token": "活动token",
    "fnCode": "invoke",
    "commParams": {
      "ubbLoc": "ttf.lqzx",
      "lid": "19_1601_50258_62859", 
      "client": 0,
      "sdkToken": "..."
    },
    "bizParams": {
      "openChannel": "jdAppHome",
      "actKey": "活动密钥",
      "rewardReceiveKey": "奖励密钥"
    }
  }
}
```

#### 原始h5st值
从抓包数据中提取的两个原始h5st值：

**comp_data_load**:
```
20250602193855954%3Bgzixaww9w3w3amw4%3Bec373%3Btk05w204358a341l...
```

**comp_data_interact**:
```
20250602193856941%3Baixipa3axq30h2w9%3B93453%3Btk03w8a3b1bc918n...
```

### 🔄 响应格式支持

现在支持以下多种h5st服务响应格式：

1. **格式1**: `{ h5st: "value" }`
2. **格式2**: `{ data: { h5st: "value" } }`
3. **格式3**: `{ result: { h5st: "value" } }`
4. **格式4**: 直接返回字符串
5. **格式5**: `{ code: 0, data: "h5st_value" }`

### 🛠️ 错误处理增强

- ✅ 详细的网络错误信息
- ✅ HTTP状态码显示
- ✅ 响应数据格式检查
- ✅ 连接超时处理

### 📁 新增文件

- `debug_h5st.js` - h5st调试工具
- `test_h5st.js` - h5st参数测试
- `CHANGELOG.md` - 更新日志

### 🔧 修改文件

- `jd_lingquan_basic.js` - 核心h5st逻辑优化
- `README.md` - 更新h5st参数说明
- `test.js` - 增加h5st参数验证

### 🚀 使用方法

1. **确保h5st服务支持新参数格式**
2. **运行调试工具检查连接**:
   ```bash
   node debug_h5st.js
   ```
3. **正常使用脚本**:
   ```bash
   node jd_lingquan.js
   ```

### ⚠️ 重要说明

- h5st服务需要支持新的请求参数格式
- 原始h5st值已内置，无需手动配置
- 如果遇到响应格式问题，请使用调试工具排查

### 🔍 调试工具

使用 `debug_h5st.js` 可以：
- 测试h5st服务连接
- 查看详细的请求/响应数据
- 验证参数格式
- 排查响应解析问题

### 📞 技术支持

如果遇到问题：
1. 运行 `node debug_h5st.js` 查看详细信息
2. 检查h5st服务是否支持新参数格式
3. 确认网络连接正常

---

## v1.0.0 (2025-01-30) - 初始版本

### 🎯 核心功能
- 基于抓包数据开发的京东领券中心抽奖脚本
- 支持多账号批量执行
- 完整的错误处理机制
- 环境变量配置支持
