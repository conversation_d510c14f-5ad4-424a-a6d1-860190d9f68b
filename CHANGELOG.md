# 更新日志

## v1.1.0 (2025-01-30) - h5st参数优化

### 🔧 主要更新

#### h5st请求参数优化
- ✅ **新增 `appid` 参数**: 所有h5st请求现在都包含 `appid: "day_day_reward"`
- ✅ **集成原始h5st值**: 从抓包数据中提取的原始h5st值作为参考参数
- ✅ **支持多种响应格式**: 兼容不同h5st服务的响应结构

#### 新的h5st请求格式
```json
{
  "appid": "day_day_reward",
  "version": "5.1", 
  "functionId": "comp_data_load | comp_data_interact",
  "pin": "用户pin",
  "ua": "用户代理字符串",
  "h5st": "原始抓包的h5st值",
  "body": {
    "token": "活动token",
    "fnCode": "invoke",
    "commParams": {
      "ubbLoc": "ttf.lqzx",
      "lid": "19_1601_50258_62859", 
      "client": 0,
      "sdkToken": "..."
    },
    "bizParams": {
      "openChannel": "jdAppHome",
      "actKey": "活动密钥",
      "rewardReceiveKey": "奖励密钥"
    }
  }
}
```

#### 原始h5st值
从抓包数据中提取的两个原始h5st值：

**comp_data_load**:
```
20250602193855954%3Bgzixaww9w3w3amw4%3Bec373%3Btk05w204358a341l...
```

**comp_data_interact**:
```
20250602193856941%3Baixipa3axq30h2w9%3B93453%3Btk03w8a3b1bc918n...
```

### 🔄 响应格式支持

现在支持以下多种h5st服务响应格式：

1. **格式1**: `{ h5st: "value" }`
2. **格式2**: `{ data: { h5st: "value" } }`
3. **格式3**: `{ result: { h5st: "value" } }`
4. **格式4**: 直接返回字符串
5. **格式5**: `{ code: 0, data: "h5st_value" }`

### 🛠️ 错误处理增强

- ✅ 详细的网络错误信息
- ✅ HTTP状态码显示
- ✅ 响应数据格式检查
- ✅ 连接超时处理

### 📁 新增文件

- `debug_h5st.js` - h5st调试工具
- `test_h5st.js` - h5st参数测试
- `CHANGELOG.md` - 更新日志

### 🔧 修改文件

- `jd_lingquan_basic.js` - 核心h5st逻辑优化
- `README.md` - 更新h5st参数说明
- `test.js` - 增加h5st参数验证

### 🚀 使用方法

1. **确保h5st服务支持新参数格式**
2. **运行调试工具检查连接**:
   ```bash
   node debug_h5st.js
   ```
3. **正常使用脚本**:
   ```bash
   node jd_lingquan.js
   ```

### ⚠️ 重要说明

- h5st服务需要支持新的请求参数格式
- 原始h5st值已内置，无需手动配置
- 如果遇到响应格式问题，请使用调试工具排查

### 🔍 调试工具

使用 `debug_h5st.js` 可以：
- 测试h5st服务连接
- 查看详细的请求/响应数据
- 验证参数格式
- 排查响应解析问题

### 📞 技术支持

如果遇到问题：
1. 运行 `node debug_h5st.js` 查看详细信息
2. 检查h5st服务是否支持新参数格式
3. 确认网络连接正常

---

## v1.0.0 (2025-01-30) - 初始版本

### 🎯 核心功能
- 基于抓包数据开发的京东领券中心抽奖脚本
- 支持多账号批量执行
- 完整的错误处理机制
- 环境变量配置支持
