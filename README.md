# 京东领券中心抽奖脚本

基于抓包分析开发的京东领券中心外卖抽奖自动化脚本。

## 功能说明

- 自动获取活动数据
- 自动领取外卖优惠券
- 支持多账号批量执行
- 支持环境变量和配置文件两种Cookie配置方式

## 活动信息

- **活动名称**: 外卖18点抽奖活动
- **活动时间**: 每天早8点到晚8点，每2小时可抽奖1次
- **奖励类型**: 外卖优惠券（通常为5元券，满6元可用）
- **活动入口**: 京东APP -> 领券中心 -> 外卖抽奖

## 安装依赖

```bash
npm install axios
```

## 配置说明

### 方式一：修改 jdCookie.js 文件

编辑 `jdCookie.js` 文件，在 `CookieJDs` 数组中添加您的Cookie：

```javascript
let CookieJDs = [
    'pt_key=your_pt_key_here;pt_pin=your_pt_pin_here;',
    // 可以添加多个账号
];
```

### 方式二：使用环境变量

设置环境变量 `JD_COOKIE`，多个账号用 `&` 分隔：

```bash
export JD_COOKIE="pt_key=xxx;pt_pin=xxx;&pt_key=yyy;pt_pin=yyy;"
```

## Cookie获取方法

1. 打开京东APP或网页版
2. 登录您的账号
3. 使用抓包工具（如Charles、Fiddler等）获取请求中的Cookie
4. 提取其中的 `pt_key` 和 `pt_pin` 字段

**Cookie格式示例**：
```
pt_key=app_openAAJoQ50VADDkgN-IltIUwZUk6F6Mh41i6m5g1vfMBYEmXjW2GMgocDmgeuqFfTWSIJQuXpCC77Y;pt_pin=jd_4b25e12eb2177;
```

## 运行脚本

```bash
node jd_lingquan.js
```

## 文件说明

- `jd_lingquan.js` - 主执行脚本
- `jd_lingquan_basic.js` - 基础请求功能模块
- `jdCookie.js` - Cookie配置文件
- `README.md` - 使用说明

## 重要说明

### h5st签名服务

脚本需要h5st签名服务支持，默认配置为：
```
http://**********:3001/h5st
```

**h5st请求参数格式**：
```json
{
  "appid": "day_day_reward",
  "version": "5.1",
  "functionId": "comp_data_load 或 comp_data_interact",
  "pin": "用户pin",
  "ua": "用户代理",
  "h5st": "原始抓包的h5st值",
  "body": {
    "token": "活动token",
    "fnCode": "invoke",
    "commParams": {},
    "bizParams": {}
  }
}
```

**重要更新**：
- 已加入 `appid` 参数
- 已包含原始抓包的 `h5st` 值作为参考
- 支持 `comp_data_load` 和 `comp_data_interact` 两种请求类型

如果您没有h5st服务，需要：
1. 搭建自己的h5st签名服务
2. 或修改代码中的h5st服务地址

### 安全提醒

- 请妥善保管您的Cookie信息
- 不要将包含真实Cookie的文件上传到公共仓库
- 建议定期更新Cookie以确保有效性

### 使用限制

- 每个账号每2小时只能参与一次
- 活动时间为每天8:00-20:00
- 请遵守京东平台规则，合理使用脚本

## 常见问题

### Q: 提示"获取h5st签名失败"
A: 检查h5st服务是否正常运行，或联系服务提供方

### Q: 提示"Cookie格式错误"
A: 确保Cookie包含完整的pt_key和pt_pin字段

### Q: 提示"今日已参与过活动"
A: 正常现象，每2小时只能参与一次

### Q: 提示"活动未开始或已结束"
A: 检查当前时间是否在活动时间范围内（8:00-20:00）

## 免责声明

本脚本仅供学习交流使用，使用者需自行承担使用风险。请遵守相关法律法规和平台规则。

## 更新日志

- v1.0.0 (2025-01-30)
  - 初始版本
  - 支持基础的抽奖功能
  - 支持多账号执行
